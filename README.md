# GP3 AI Chat Application

A cross-platform AI chat application built with Domain-Driven Design principles, featuring project management and multiple AI model support.

## 🏗️ Architecture

This application follows **Domain-Driven Design (DDD)** and **Hexagonal Architecture** patterns with a **monorepo** structure.

### Technology Stack

- **Frontend**: 
  - **Web**: Next.js 14 with Material UI
  - **Mobile**: Expo + React Native with React Native Paper
- **Backend**: Next.js API routes with tRPC
- **Database**: Prisma ORM with SQLite (dev) / PostgreSQL (prod)
- **AI Integration**: Vercel AI SDK with OpenAI API
- **Authentication**: Auth0 (to be implemented)
- **Real-time**: tRPC subscriptions with WebSockets (to be implemented)
- **State Management**: Zustand + tRPC React Query integration

### AI Models

- **Small** (GPT-4.1-mini) - Fast & Economical
- **Medium** (GPT-4o) - Balanced (Default)
- **Large** (GPT-o3) - Most Capable

## 📁 Project Structure

```
gp3-ai-chat/
├── apps/
│   ├── web/                    # Next.js web application
│   └── mobile/                 # Expo React Native app
├── packages/
│   ├── shared/                 # Shared types and schemas
│   ├── database/               # Prisma schema and client
│   ├── server/                 # tRPC server logic
│   └── trpc/                   # tRPC client setup
└── README.md
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Set up the database:**
   ```bash
   cd packages/database
   DATABASE_URL="file:./dev.db" npx prisma db push
   DATABASE_URL="file:./dev.db" npx tsx prisma/seed.ts
   ```

3. **Start the web application:**
   ```bash
   cd apps/web
   npm run dev
   ```

4. **Start the mobile application (optional):**
   ```bash
   cd apps/mobile
   npm start
   ```

### Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
# Database
DATABASE_URL="file:./dev.db"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# Auth0 (to be implemented)
AUTH0_DOMAIN="your-domain.auth0.com"
AUTH0_CLIENT_ID="your-client-id"
AUTH0_CLIENT_SECRET="your-client-secret"
```

## ✅ Current Implementation Status

### ✅ Completed Features

1. **Project Structure & Setup**
   - ✅ Monorepo with Turborepo
   - ✅ TypeScript configuration
   - ✅ Package dependencies

2. **Database & Schema**
   - ✅ Prisma schema with SQLite
   - ✅ User, Project, ChatSession, Message, UsageRecord models
   - ✅ Database seeding script

3. **Backend (tRPC)**
   - ✅ tRPC server setup
   - ✅ Projects router (CRUD operations)
   - ✅ Chats router (basic operations)
   - ✅ Usage tracking router
   - ✅ Authentication router (placeholder)

4. **Web Frontend**
   - ✅ Next.js with Material UI
   - ✅ Project management interface
   - ✅ Chat session management
   - ✅ Basic chat interface
   - ✅ tRPC integration

5. **Mobile Frontend**
   - ✅ Expo React Native setup
   - ✅ React Native Paper UI
   - ✅ Basic project screen structure

### 🚧 In Progress / Next Steps

1. **AI Integration**
   - [ ] OpenAI API integration with Vercel AI SDK
   - [ ] Streaming responses
   - [ ] Token counting and usage tracking
   - [ ] Model selection implementation

2. **Authentication**
   - [ ] Auth0 integration
   - [ ] User session management
   - [ ] Protected routes

3. **Real-time Features**
   - [ ] WebSocket setup for live chat updates
   - [ ] tRPC subscriptions
   - [ ] Cross-device synchronization

4. **Mobile App Completion**
   - [ ] Project detail screen
   - [ ] Chat interface
   - [ ] Navigation setup
   - [ ] Platform-specific optimizations

5. **Advanced Features**
   - [ ] Usage billing and cost estimation
   - [ ] Chat export/import
   - [ ] Search functionality
   - [ ] Settings and preferences

6. **Production Readiness**
   - [ ] Error handling and validation
   - [ ] Loading states and optimistic updates
   - [ ] PostgreSQL migration
   - [ ] Deployment configuration
   - [ ] Testing suite

## 🧪 Testing the Current Implementation

1. **Start the web application:**
   ```bash
   cd apps/web && npm run dev
   ```

2. **Open http://localhost:3000**

3. **You should see:**
   - A sample project "Sample AI Project"
   - Ability to create new projects
   - Navigation to project details
   - Basic chat interface (with echo responses)

## 🔧 Development Commands

```bash
# Install dependencies
npm install

# Start web development server
cd apps/web && npm run dev

# Start mobile development server
cd apps/mobile && npm start

# Database operations
cd packages/database
DATABASE_URL="file:./dev.db" npx prisma db push
DATABASE_URL="file:./dev.db" npx prisma studio
DATABASE_URL="file:./dev.db" npx tsx prisma/seed.ts

# Build all packages
npm run build

# Type checking
npm run type-check
```

## 📝 Notes

- Currently using mock authentication (test user)
- AI responses are placeholder echo responses
- Mobile app structure is set up but needs completion
- Real-time features are planned but not implemented
- Usage tracking is implemented but not connected to actual AI usage

The foundation is solid and ready for the next phase of development!
