// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  auth0Id   String   @unique
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  projects     Project[]
  usageRecords UsageRecord[]
  
  @@map("users")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  chatSessions ChatSession[]
  
  @@map("projects")
}

model ChatSession {
  id        String   @id @default(cuid())
  title     String
  model     String   @default("MEDIUM") // SMALL, MEDIUM, LARGE
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  projectId String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  messages     Message[]
  usageRecords UsageRecord[]

  @@map("chat_sessions")
}

model Message {
  id        String      @id @default(cuid())
  content   String
  role      String      // USER, ASSISTANT, SYSTEM
  tokenCount Int?        // For usage tracking
  createdAt DateTime    @default(now())

  chatSessionId String
  chatSession   ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model UsageRecord {
  id            String   @id @default(cuid())
  model         String   // SMALL, MEDIUM, LARGE
  inputTokens   Int
  outputTokens  Int
  totalTokens   Int
  estimatedCost Float    // Using Float for SQLite compatibility
  createdAt     DateTime @default(now())

  userId        String
  user          User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  chatSessionId String?
  chatSession   ChatSession? @relation(fields: [chatSessionId], references: [id], onDelete: SetNull)

  @@map("usage_records")
}

// Note: SQLite doesn't support enums, so we use strings with validation in the application layer
// AIModel: SMALL, MEDIUM, LARGE
// MessageRole: USER, ASSISTANT, SYSTEM
