import { inferAsyncReturnType } from '@trpc/server';
import { CreateNextContextOptions } from '@trpc/server/adapters/next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function createTRPCContext(opts: CreateNextContextOptions) {
  const { req, res } = opts;

  // Get user from session (we'll implement this with Auth0)
  async function getUserFromHeader() {
    // TODO: Implement Auth0 token verification
    // For now, return a mock user for testing
    return {
      id: 'test-user-id',
      auth0Id: 'test-auth0-id',
      email: '<EMAIL>',
      name: 'Test User',
    };
  }

  const user = await getUserFromHeader();

  return {
    req,
    res,
    prisma,
    user,
  };
}

export type Context = inferAsyncReturnType<typeof createTRPCContext>;
