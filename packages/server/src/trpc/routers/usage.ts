import { z } from 'zod';
import { router, protectedProcedure } from '../trpc';

// Inline schemas for now to avoid import issues
const usageQuerySchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

const estimateCostSchema = z.object({
  model: z.enum(['SMALL', 'MEDIUM', 'LARGE']),
  messageCount: z.number().min(1),
});

// Model pricing per 1K tokens (approximate OpenAI pricing)
const MODEL_PRICING = {
  SMALL: 0.0001,   // GPT-4.1-mini
  MEDIUM: 0.0003,  // GPT-4o
  LARGE: 0.0006,   // GPT-o3
} as const;

export const usageRouter = router({
  // Get current month usage
  getCurrentUsage: protectedProcedure
    .query(async ({ ctx }) => {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const usageRecords = await ctx.prisma.usageRecord.findMany({
        where: {
          userId: ctx.user.id,
          createdAt: {
            gte: startOfMonth,
          },
        },
      });

      const summary = usageRecords.reduce(
        (acc, record) => {
          acc.totalTokens += record.totalTokens;
          acc.totalCost += record.estimatedCost;

          if (!acc.byModel[record.model]) {
            acc.byModel[record.model] = { tokens: 0, cost: 0 };
          }

          acc.byModel[record.model].tokens += record.totalTokens;
          acc.byModel[record.model].cost += record.estimatedCost;

          return acc;
        },
        {
          totalTokens: 0,
          totalCost: 0,
          byModel: {} as Record<string, { tokens: number; cost: number }>,
        }
      );

      return summary;
    }),

  // Get usage history
  getUsageHistory: protectedProcedure
    .input(usageQuerySchema)
    .query(async ({ ctx, input }) => {
      const where: any = {
        userId: ctx.user.id,
      };

      if (input.startDate || input.endDate) {
        where.createdAt = {};
        if (input.startDate) {
          where.createdAt.gte = input.startDate;
        }
        if (input.endDate) {
          where.createdAt.lte = input.endDate;
        }
      }

      const usageRecords = await ctx.prisma.usageRecord.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        include: {
          chatSession: {
            select: {
              title: true,
              project: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      return usageRecords;
    }),

  // Estimate cost for usage
  getEstimatedCost: protectedProcedure
    .input(estimateCostSchema)
    .query(async ({ ctx, input }) => {
      const averageTokensPerMessage = 100; // Rough estimate
      const totalTokens = input.messageCount * averageTokensPerMessage;
      const costPerToken = MODEL_PRICING[input.model];
      const estimatedCost = (totalTokens / 1000) * costPerToken;

      return {
        messageCount: input.messageCount,
        estimatedTokens: totalTokens,
        estimatedCost,
        model: input.model,
      };
    }),
});
