import { z } from 'zod';
import { router, protectedProcedure } from '../trpc';

// Inline schemas for now to avoid import issues
const createProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(100),
  description: z.string().max(500).optional(),
});

const updateProjectSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Project name is required').max(100).optional(),
  description: z.string().max(500).optional(),
});

export const projectsRouter = router({
  // List user's projects
  list: protectedProcedure
    .query(async ({ ctx }) => {
      const projects = await ctx.prisma.project.findMany({
        where: { userId: ctx.user.id },
        orderBy: { updatedAt: 'desc' },
        include: {
          _count: {
            select: { chatSessions: true },
          },
        },
      });

      return projects;
    }),

  // Get single project
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await ctx.prisma.project.findFirst({
        where: {
          id: input.id,
          userId: ctx.user.id,
        },
        include: {
          _count: {
            select: { chatSessions: true },
          },
        },
      });

      if (!project) {
        throw new Error('Project not found');
      }

      return project;
    }),

  // Create new project
  create: protectedProcedure
    .input(createProjectSchema)
    .mutation(async ({ ctx, input }) => {
      const project = await ctx.prisma.project.create({
        data: {
          ...input,
          userId: ctx.user.id,
        },
      });

      return project;
    }),

  // Update project
  update: protectedProcedure
    .input(updateProjectSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      const project = await ctx.prisma.project.updateMany({
        where: {
          id,
          userId: ctx.user.id,
        },
        data: updateData,
      });

      if (project.count === 0) {
        throw new Error('Project not found or unauthorized');
      }

      return await ctx.prisma.project.findUnique({
        where: { id },
      });
    }),

  // Delete project
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.project.deleteMany({
        where: {
          id: input.id,
          userId: ctx.user.id,
        },
      });

      if (result.count === 0) {
        throw new Error('Project not found or unauthorized');
      }

      return { success: true };
    }),
});
