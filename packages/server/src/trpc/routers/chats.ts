import { z } from 'zod';
import { router, protectedProcedure } from '../trpc';

// Inline schemas for now to avoid import issues
const createChatSessionSchema = z.object({
  projectId: z.string(),
  title: z.string().min(1, 'Chat title is required').max(200),
  model: z.enum(['SMALL', 'MEDIUM', 'LARGE']).default('MEDIUM'),
});

const sendMessageSchema = z.object({
  chatSessionId: z.string(),
  content: z.string().min(1, 'Message content is required'),
  model: z.enum(['SMALL', 'MEDIUM', 'LARGE']).optional(),
});

export const chatsRouter = router({
  // List chat sessions for a project
  list: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Verify user owns the project
      const project = await ctx.prisma.project.findFirst({
        where: {
          id: input.projectId,
          userId: ctx.user.id,
        },
      });

      if (!project) {
        throw new Error('Project not found or unauthorized');
      }

      const chatSessions = await ctx.prisma.chatSession.findMany({
        where: { projectId: input.projectId },
        orderBy: { updatedAt: 'desc' },
        include: {
          _count: {
            select: { messages: true },
          },
        },
      });

      return chatSessions;
    }),

  // Get chat session with messages
  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const chatSession = await ctx.prisma.chatSession.findFirst({
        where: {
          id: input.id,
          project: {
            userId: ctx.user.id,
          },
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
          },
          project: true,
        },
      });

      if (!chatSession) {
        throw new Error('Chat session not found or unauthorized');
      }

      return chatSession;
    }),

  // Create new chat session
  create: protectedProcedure
    .input(createChatSessionSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify user owns the project
      const project = await ctx.prisma.project.findFirst({
        where: {
          id: input.projectId,
          userId: ctx.user.id,
        },
      });

      if (!project) {
        throw new Error('Project not found or unauthorized');
      }

      const chatSession = await ctx.prisma.chatSession.create({
        data: input,
      });

      return chatSession;
    }),

  // Delete chat session
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.chatSession.deleteMany({
        where: {
          id: input.id,
          project: {
            userId: ctx.user.id,
          },
        },
      });

      if (result.count === 0) {
        throw new Error('Chat session not found or unauthorized');
      }

      return { success: true };
    }),

  // Send message (we'll implement AI integration later)
  sendMessage: protectedProcedure
    .input(sendMessageSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify user owns the chat session
      const chatSession = await ctx.prisma.chatSession.findFirst({
        where: {
          id: input.chatSessionId,
          project: {
            userId: ctx.user.id,
          },
        },
      });

      if (!chatSession) {
        throw new Error('Chat session not found or unauthorized');
      }

      // Create user message
      const userMessage = await ctx.prisma.message.create({
        data: {
          content: input.content,
          role: 'USER',
          chatSessionId: input.chatSessionId,
        },
      });

      // TODO: Implement AI response generation
      // For now, create a simple echo response
      const assistantMessage = await ctx.prisma.message.create({
        data: {
          content: `Echo: ${input.content}`,
          role: 'ASSISTANT',
          chatSessionId: input.chatSessionId,
        },
      });

      // Update chat session timestamp
      await ctx.prisma.chatSession.update({
        where: { id: input.chatSessionId },
        data: { updatedAt: new Date() },
      });

      return {
        userMessage,
        assistantMessage,
      };
    }),
});
