"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/trpc/[trpc]";
exports.ids = ["pages/api/trpc/[trpc]"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "@trpc/server":
/*!*******************************!*\
  !*** external "@trpc/server" ***!
  \*******************************/
/***/ ((module) => {

module.exports = import("@trpc/server");;

/***/ }),

/***/ "@trpc/server/adapters/next":
/*!*********************************************!*\
  !*** external "@trpc/server/adapters/next" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = import("@trpc/server/adapters/next");;

/***/ }),

/***/ "zod":
/*!**********************!*\
  !*** external "zod" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("zod");;

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Ftrpc%2F%5Btrpc%5D.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Ftrpc%2F%5Btrpc%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_trpc_trpc_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/pages/api/trpc/[trpc].ts */ \"(api)/./src/pages/api/trpc/[trpc].ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_trpc_trpc_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_trpc_trpc_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_trpc_trpc_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_trpc_trpc_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/trpc/[trpc]\",\n        pathname: \"/api/trpc/[trpc]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_trpc_trpc_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Ftrpc%2F%5Btrpc%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/trpc/[trpc].ts":
/*!**************************************!*\
  !*** ./src/pages/api/trpc/[trpc].ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_adapters_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/adapters/next */ \"@trpc/server/adapters/next\");\n/* harmony import */ var _gp3_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @gp3/server */ \"(api)/../../packages/server/src/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc_server_adapters_next__WEBPACK_IMPORTED_MODULE_0__, _gp3_server__WEBPACK_IMPORTED_MODULE_1__]);\n([_trpc_server_adapters_next__WEBPACK_IMPORTED_MODULE_0__, _gp3_server__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// export API handler\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_trpc_server_adapters_next__WEBPACK_IMPORTED_MODULE_0__.createNextApiHandler)({\n    router: _gp3_server__WEBPACK_IMPORTED_MODULE_1__.appRouter,\n    createContext: _gp3_server__WEBPACK_IMPORTED_MODULE_1__.createTRPCContext,\n    onError:  true ? ({ path, error })=>{\n        console.error(`❌ tRPC failed on ${path ?? \"<no-path>\"}: ${error.message}`);\n    } : 0\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvcGFnZXMvYXBpL3RycGMvW3RycGNdLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRTtBQUNQO0FBRTNELHFCQUFxQjtBQUNyQixpRUFBZUEsZ0ZBQW9CQSxDQUFDO0lBQ2xDRyxRQUFRRixrREFBU0E7SUFDakJHLGVBQWVGLDBEQUFpQkE7SUFDaENHLFNBQ0VDLEtBQXlCLEdBQ3JCLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUU7UUFDZEMsUUFBUUQsS0FBSyxDQUNYLENBQUMsaUJBQWlCLEVBQUVELFFBQVEsWUFBWSxFQUFFLEVBQUVDLE1BQU1FLE9BQU8sQ0FBQyxDQUFDO0lBRS9ELElBQ0FDLENBQVNBO0FBQ2pCLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncDMvd2ViLy4vc3JjL3BhZ2VzL2FwaS90cnBjL1t0cnBjXS50cz9jZTUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZU5leHRBcGlIYW5kbGVyIH0gZnJvbSAnQHRycGMvc2VydmVyL2FkYXB0ZXJzL25leHQnO1xuaW1wb3J0IHsgYXBwUm91dGVyLCBjcmVhdGVUUlBDQ29udGV4dCB9IGZyb20gJ0BncDMvc2VydmVyJztcblxuLy8gZXhwb3J0IEFQSSBoYW5kbGVyXG5leHBvcnQgZGVmYXVsdCBjcmVhdGVOZXh0QXBpSGFuZGxlcih7XG4gIHJvdXRlcjogYXBwUm91dGVyLFxuICBjcmVhdGVDb250ZXh0OiBjcmVhdGVUUlBDQ29udGV4dCxcbiAgb25FcnJvcjpcbiAgICBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50J1xuICAgICAgPyAoeyBwYXRoLCBlcnJvciB9KSA9PiB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIGDinYwgdFJQQyBmYWlsZWQgb24gJHtwYXRoID8/ICc8bm8tcGF0aD4nfTogJHtlcnJvci5tZXNzYWdlfWBcbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICA6IHVuZGVmaW5lZCxcbn0pO1xuIl0sIm5hbWVzIjpbImNyZWF0ZU5leHRBcGlIYW5kbGVyIiwiYXBwUm91dGVyIiwiY3JlYXRlVFJQQ0NvbnRleHQiLCJyb3V0ZXIiLCJjcmVhdGVDb250ZXh0Iiwib25FcnJvciIsInByb2Nlc3MiLCJwYXRoIiwiZXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/trpc/[trpc].ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/server/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _trpc_root__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./trpc/root */ \"(api)/../../packages/server/src/trpc/root.ts\");\n/* harmony import */ var _trpc_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./trpc/context */ \"(api)/../../packages/server/src/trpc/context.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _trpc_context__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _trpc_context__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _trpc_trpc__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./trpc/trpc */ \"(api)/../../packages/server/src/trpc/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc_root__WEBPACK_IMPORTED_MODULE_0__, _trpc_trpc__WEBPACK_IMPORTED_MODULE_2__]);\n([_trpc_root__WEBPACK_IMPORTED_MODULE_0__, _trpc_trpc__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _trpc_root__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _trpc_root__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _trpc_trpc__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _trpc_trpc__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vcGFja2FnZXMvc2VydmVyL3NyYy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQTRCO0FBQ0c7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL0BncDMvd2ViLy4uLy4uL3BhY2thZ2VzL3NlcnZlci9zcmMvaW5kZXgudHM/YmM3MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3RycGMvcm9vdCc7XG5leHBvcnQgKiBmcm9tICcuL3RycGMvY29udGV4dCc7XG5leHBvcnQgKiBmcm9tICcuL3RycGMvdHJwYyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/index.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/context.ts":
/*!*************************************************!*\
  !*** ../../packages/server/src/trpc/context.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTRPCContext: () => (/* binding */ createTRPCContext)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nasync function createTRPCContext(opts) {\n    const { req, res } = opts;\n    // Get user from session (we'll implement this with Auth0)\n    async function getUserFromHeader() {\n        // TODO: Implement Auth0 token verification\n        // For now, return a mock user for testing\n        return {\n            id: \"test-user-id\",\n            auth0Id: \"test-auth0-id\",\n            email: \"<EMAIL>\",\n            name: \"Test User\"\n        };\n    }\n    const user = await getUserFromHeader();\n    return {\n        req,\n        res,\n        prisma,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/context.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/root.ts":
/*!**********************************************!*\
  !*** ../../packages/server/src/trpc/root.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appRouter: () => (/* binding */ appRouter)\n/* harmony export */ });\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./trpc */ \"(api)/../../packages/server/src/trpc/trpc.ts\");\n/* harmony import */ var _routers_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./routers/auth */ \"(api)/../../packages/server/src/trpc/routers/auth.ts\");\n/* harmony import */ var _routers_projects__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./routers/projects */ \"(api)/../../packages/server/src/trpc/routers/projects.ts\");\n/* harmony import */ var _routers_chats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./routers/chats */ \"(api)/../../packages/server/src/trpc/routers/chats.ts\");\n/* harmony import */ var _routers_usage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./routers/usage */ \"(api)/../../packages/server/src/trpc/routers/usage.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc__WEBPACK_IMPORTED_MODULE_0__, _routers_auth__WEBPACK_IMPORTED_MODULE_1__, _routers_projects__WEBPACK_IMPORTED_MODULE_2__, _routers_chats__WEBPACK_IMPORTED_MODULE_3__, _routers_usage__WEBPACK_IMPORTED_MODULE_4__]);\n([_trpc__WEBPACK_IMPORTED_MODULE_0__, _routers_auth__WEBPACK_IMPORTED_MODULE_1__, _routers_projects__WEBPACK_IMPORTED_MODULE_2__, _routers_chats__WEBPACK_IMPORTED_MODULE_3__, _routers_usage__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst appRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_0__.router)({\n    auth: _routers_auth__WEBPACK_IMPORTED_MODULE_1__.authRouter,\n    projects: _routers_projects__WEBPACK_IMPORTED_MODULE_2__.projectsRouter,\n    chats: _routers_chats__WEBPACK_IMPORTED_MODULE_3__.chatsRouter,\n    usage: _routers_usage__WEBPACK_IMPORTED_MODULE_4__.usageRouter\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vcGFja2FnZXMvc2VydmVyL3NyYy90cnBjL3Jvb3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQWdDO0FBQ1k7QUFDUTtBQUNOO0FBQ0E7QUFFdkMsTUFBTUssWUFBWUwsNkNBQU1BLENBQUM7SUFDOUJNLE1BQU1MLHFEQUFVQTtJQUNoQk0sVUFBVUwsNkRBQWNBO0lBQ3hCTSxPQUFPTCx1REFBV0E7SUFDbEJNLE9BQU9MLHVEQUFXQTtBQUNwQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdwMy93ZWIvLi4vLi4vcGFja2FnZXMvc2VydmVyL3NyYy90cnBjL3Jvb3QudHM/ODc1YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByb3V0ZXIgfSBmcm9tICcuL3RycGMnO1xuaW1wb3J0IHsgYXV0aFJvdXRlciB9IGZyb20gJy4vcm91dGVycy9hdXRoJztcbmltcG9ydCB7IHByb2plY3RzUm91dGVyIH0gZnJvbSAnLi9yb3V0ZXJzL3Byb2plY3RzJztcbmltcG9ydCB7IGNoYXRzUm91dGVyIH0gZnJvbSAnLi9yb3V0ZXJzL2NoYXRzJztcbmltcG9ydCB7IHVzYWdlUm91dGVyIH0gZnJvbSAnLi9yb3V0ZXJzL3VzYWdlJztcblxuZXhwb3J0IGNvbnN0IGFwcFJvdXRlciA9IHJvdXRlcih7XG4gIGF1dGg6IGF1dGhSb3V0ZXIsXG4gIHByb2plY3RzOiBwcm9qZWN0c1JvdXRlcixcbiAgY2hhdHM6IGNoYXRzUm91dGVyLFxuICB1c2FnZTogdXNhZ2VSb3V0ZXIsXG59KTtcblxuZXhwb3J0IHR5cGUgQXBwUm91dGVyID0gdHlwZW9mIGFwcFJvdXRlcjtcbiJdLCJuYW1lcyI6WyJyb3V0ZXIiLCJhdXRoUm91dGVyIiwicHJvamVjdHNSb3V0ZXIiLCJjaGF0c1JvdXRlciIsInVzYWdlUm91dGVyIiwiYXBwUm91dGVyIiwiYXV0aCIsInByb2plY3RzIiwiY2hhdHMiLCJ1c2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/root.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/routers/auth.ts":
/*!******************************************************!*\
  !*** ../../packages/server/src/trpc/routers/auth.ts ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authRouter: () => (/* binding */ authRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"zod\");\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../trpc */ \"(api)/../../packages/server/src/trpc/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__]);\n([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst authRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_1__.router)({\n    // Get current user\n    me: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.query(async ({ ctx })=>{\n        const user = await ctx.prisma.user.findUnique({\n            where: {\n                id: ctx.user.id\n            }\n        });\n        if (!user) {\n            throw new Error(\"User not found\");\n        }\n        return user;\n    }),\n    // Create or update user (called during Auth0 callback)\n    upsertUser: _trpc__WEBPACK_IMPORTED_MODULE_1__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        auth0Id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(),\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n    })).mutation(async ({ ctx, input })=>{\n        const user = await ctx.prisma.user.upsert({\n            where: {\n                auth0Id: input.auth0Id\n            },\n            update: {\n                email: input.email,\n                name: input.name\n            },\n            create: {\n                auth0Id: input.auth0Id,\n                email: input.email,\n                name: input.name\n            }\n        });\n        return user;\n    })\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/routers/auth.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/routers/chats.ts":
/*!*******************************************************!*\
  !*** ../../packages/server/src/trpc/routers/chats.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatsRouter: () => (/* binding */ chatsRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"zod\");\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../trpc */ \"(api)/../../packages/server/src/trpc/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__]);\n([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Inline schemas for now to avoid import issues\nconst createChatSessionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    projectId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Chat title is required\").max(200),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        \"SMALL\",\n        \"MEDIUM\",\n        \"LARGE\"\n    ]).default(\"MEDIUM\")\n});\nconst sendMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    chatSessionId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Message content is required\"),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        \"SMALL\",\n        \"MEDIUM\",\n        \"LARGE\"\n    ]).optional()\n});\nconst chatsRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_1__.router)({\n    // List chat sessions for a project\n    list: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        projectId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })).query(async ({ ctx, input })=>{\n        // Verify user owns the project\n        const project = await ctx.prisma.project.findFirst({\n            where: {\n                id: input.projectId,\n                userId: ctx.user.id\n            }\n        });\n        if (!project) {\n            throw new Error(\"Project not found or unauthorized\");\n        }\n        const chatSessions = await ctx.prisma.chatSession.findMany({\n            where: {\n                projectId: input.projectId\n            },\n            orderBy: {\n                updatedAt: \"desc\"\n            },\n            include: {\n                _count: {\n                    select: {\n                        messages: true\n                    }\n                }\n            }\n        });\n        return chatSessions;\n    }),\n    // Get chat session with messages\n    get: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })).query(async ({ ctx, input })=>{\n        const chatSession = await ctx.prisma.chatSession.findFirst({\n            where: {\n                id: input.id,\n                project: {\n                    userId: ctx.user.id\n                }\n            },\n            include: {\n                messages: {\n                    orderBy: {\n                        createdAt: \"asc\"\n                    }\n                },\n                project: true\n            }\n        });\n        if (!chatSession) {\n            throw new Error(\"Chat session not found or unauthorized\");\n        }\n        return chatSession;\n    }),\n    // Create new chat session\n    create: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(createChatSessionSchema).mutation(async ({ ctx, input })=>{\n        // Verify user owns the project\n        const project = await ctx.prisma.project.findFirst({\n            where: {\n                id: input.projectId,\n                userId: ctx.user.id\n            }\n        });\n        if (!project) {\n            throw new Error(\"Project not found or unauthorized\");\n        }\n        const chatSession = await ctx.prisma.chatSession.create({\n            data: input\n        });\n        return chatSession;\n    }),\n    // Delete chat session\n    delete: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })).mutation(async ({ ctx, input })=>{\n        const result = await ctx.prisma.chatSession.deleteMany({\n            where: {\n                id: input.id,\n                project: {\n                    userId: ctx.user.id\n                }\n            }\n        });\n        if (result.count === 0) {\n            throw new Error(\"Chat session not found or unauthorized\");\n        }\n        return {\n            success: true\n        };\n    }),\n    // Send message (we'll implement AI integration later)\n    sendMessage: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(sendMessageSchema).mutation(async ({ ctx, input })=>{\n        // Verify user owns the chat session\n        const chatSession = await ctx.prisma.chatSession.findFirst({\n            where: {\n                id: input.chatSessionId,\n                project: {\n                    userId: ctx.user.id\n                }\n            }\n        });\n        if (!chatSession) {\n            throw new Error(\"Chat session not found or unauthorized\");\n        }\n        // Create user message\n        const userMessage = await ctx.prisma.message.create({\n            data: {\n                content: input.content,\n                role: \"USER\",\n                chatSessionId: input.chatSessionId\n            }\n        });\n        // TODO: Implement AI response generation\n        // For now, create a simple echo response\n        const assistantMessage = await ctx.prisma.message.create({\n            data: {\n                content: `Echo: ${input.content}`,\n                role: \"ASSISTANT\",\n                chatSessionId: input.chatSessionId\n            }\n        });\n        // Update chat session timestamp\n        await ctx.prisma.chatSession.update({\n            where: {\n                id: input.chatSessionId\n            },\n            data: {\n                updatedAt: new Date()\n            }\n        });\n        return {\n            userMessage,\n            assistantMessage\n        };\n    })\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/routers/chats.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/routers/projects.ts":
/*!**********************************************************!*\
  !*** ../../packages/server/src/trpc/routers/projects.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   projectsRouter: () => (/* binding */ projectsRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"zod\");\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../trpc */ \"(api)/../../packages/server/src/trpc/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__]);\n([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Inline schemas for now to avoid import issues\nconst createProjectSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Project name is required\").max(100),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(500).optional()\n});\nconst updateProjectSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Project name is required\").max(100).optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(500).optional()\n});\nconst projectsRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_1__.router)({\n    // List user's projects\n    list: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.query(async ({ ctx })=>{\n        const projects = await ctx.prisma.project.findMany({\n            where: {\n                userId: ctx.user.id\n            },\n            orderBy: {\n                updatedAt: \"desc\"\n            },\n            include: {\n                _count: {\n                    select: {\n                        chatSessions: true\n                    }\n                }\n            }\n        });\n        return projects;\n    }),\n    // Get single project\n    get: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })).query(async ({ ctx, input })=>{\n        const project = await ctx.prisma.project.findFirst({\n            where: {\n                id: input.id,\n                userId: ctx.user.id\n            },\n            include: {\n                _count: {\n                    select: {\n                        chatSessions: true\n                    }\n                }\n            }\n        });\n        if (!project) {\n            throw new Error(\"Project not found\");\n        }\n        return project;\n    }),\n    // Create new project\n    create: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(createProjectSchema).mutation(async ({ ctx, input })=>{\n        const project = await ctx.prisma.project.create({\n            data: {\n                ...input,\n                userId: ctx.user.id\n            }\n        });\n        return project;\n    }),\n    // Update project\n    update: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(updateProjectSchema).mutation(async ({ ctx, input })=>{\n        const { id, ...updateData } = input;\n        const project = await ctx.prisma.project.updateMany({\n            where: {\n                id,\n                userId: ctx.user.id\n            },\n            data: updateData\n        });\n        if (project.count === 0) {\n            throw new Error(\"Project not found or unauthorized\");\n        }\n        return await ctx.prisma.project.findUnique({\n            where: {\n                id\n            }\n        });\n    }),\n    // Delete project\n    delete: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    })).mutation(async ({ ctx, input })=>{\n        const result = await ctx.prisma.project.deleteMany({\n            where: {\n                id: input.id,\n                userId: ctx.user.id\n            }\n        });\n        if (result.count === 0) {\n            throw new Error(\"Project not found or unauthorized\");\n        }\n        return {\n            success: true\n        };\n    })\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/routers/projects.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/routers/usage.ts":
/*!*******************************************************!*\
  !*** ../../packages/server/src/trpc/routers/usage.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usageRouter: () => (/* binding */ usageRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"zod\");\n/* harmony import */ var _trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../trpc */ \"(api)/../../packages/server/src/trpc/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__]);\n([zod__WEBPACK_IMPORTED_MODULE_0__, _trpc__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Inline schemas for now to avoid import issues\nconst usageQuerySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    startDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.date().optional(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.date().optional()\n});\nconst estimateCostSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        \"SMALL\",\n        \"MEDIUM\",\n        \"LARGE\"\n    ]),\n    messageCount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(1)\n});\n// Model pricing per 1K tokens (approximate OpenAI pricing)\nconst MODEL_PRICING = {\n    SMALL: 0.0001,\n    MEDIUM: 0.0003,\n    LARGE: 0.0006\n};\nconst usageRouter = (0,_trpc__WEBPACK_IMPORTED_MODULE_1__.router)({\n    // Get current month usage\n    getCurrentUsage: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.query(async ({ ctx })=>{\n        const now = new Date();\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n        const usageRecords = await ctx.prisma.usageRecord.findMany({\n            where: {\n                userId: ctx.user.id,\n                createdAt: {\n                    gte: startOfMonth\n                }\n            }\n        });\n        const summary = usageRecords.reduce((acc, record)=>{\n            acc.totalTokens += record.totalTokens;\n            acc.totalCost += record.estimatedCost;\n            if (!acc.byModel[record.model]) {\n                acc.byModel[record.model] = {\n                    tokens: 0,\n                    cost: 0\n                };\n            }\n            acc.byModel[record.model].tokens += record.totalTokens;\n            acc.byModel[record.model].cost += record.estimatedCost;\n            return acc;\n        }, {\n            totalTokens: 0,\n            totalCost: 0,\n            byModel: {}\n        });\n        return summary;\n    }),\n    // Get usage history\n    getUsageHistory: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(usageQuerySchema).query(async ({ ctx, input })=>{\n        const where = {\n            userId: ctx.user.id\n        };\n        if (input.startDate || input.endDate) {\n            where.createdAt = {};\n            if (input.startDate) {\n                where.createdAt.gte = input.startDate;\n            }\n            if (input.endDate) {\n                where.createdAt.lte = input.endDate;\n            }\n        }\n        const usageRecords = await ctx.prisma.usageRecord.findMany({\n            where,\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            include: {\n                chatSession: {\n                    select: {\n                        title: true,\n                        project: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return usageRecords;\n    }),\n    // Estimate cost for usage\n    getEstimatedCost: _trpc__WEBPACK_IMPORTED_MODULE_1__.protectedProcedure.input(estimateCostSchema).query(async ({ ctx, input })=>{\n        const averageTokensPerMessage = 100; // Rough estimate\n        const totalTokens = input.messageCount * averageTokensPerMessage;\n        const costPerToken = MODEL_PRICING[input.model];\n        const estimatedCost = totalTokens / 1000 * costPerToken;\n        return {\n            messageCount: input.messageCount,\n            estimatedTokens: totalTokens,\n            estimatedCost,\n            model: input.model\n        };\n    })\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/routers/usage.ts\n");

/***/ }),

/***/ "(api)/../../packages/server/src/trpc/trpc.ts":
/*!**********************************************!*\
  !*** ../../packages/server/src/trpc/trpc.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeRouters: () => (/* binding */ mergeRouters),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   protectedProcedure: () => (/* binding */ protectedProcedure),\n/* harmony export */   publicProcedure: () => (/* binding */ publicProcedure),\n/* harmony export */   router: () => (/* binding */ router)\n/* harmony export */ });\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server */ \"@trpc/server\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"zod\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc_server__WEBPACK_IMPORTED_MODULE_0__, zod__WEBPACK_IMPORTED_MODULE_1__]);\n([_trpc_server__WEBPACK_IMPORTED_MODULE_0__, zod__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst t = _trpc_server__WEBPACK_IMPORTED_MODULE_0__.initTRPC.context().create({\n    errorFormatter (opts) {\n        const { shape, error } = opts;\n        return {\n            ...shape,\n            data: {\n                ...shape.data,\n                zodError: error.code === \"BAD_REQUEST\" && error.cause instanceof zod__WEBPACK_IMPORTED_MODULE_1__.ZodError ? error.cause.flatten() : null\n            }\n        };\n    }\n});\nconst router = t.router;\nconst publicProcedure = t.procedure;\n// Protected procedure that requires authentication\nconst protectedProcedure = t.procedure.use(({ ctx, next })=>{\n    if (!ctx.user) {\n        throw new _trpc_server__WEBPACK_IMPORTED_MODULE_0__.TRPCError({\n            code: \"UNAUTHORIZED\"\n        });\n    }\n    return next({\n        ctx: {\n            ...ctx,\n            user: ctx.user\n        }\n    });\n});\nconst middleware = t.middleware;\nconst mergeRouters = t.mergeRouters;\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/server/src/trpc/trpc.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Ftrpc%2F%5Btrpc%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();