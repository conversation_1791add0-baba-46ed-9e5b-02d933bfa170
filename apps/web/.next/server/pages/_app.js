"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/lib/trpc.ts":
/*!*************************!*\
  !*** ./src/lib/trpc.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpc: () => (/* binding */ trpc)\n/* harmony export */ });\n/* harmony import */ var _trpc_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/next */ \"@trpc/next\");\n/* harmony import */ var _trpc_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/client */ \"@trpc/client\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc_next__WEBPACK_IMPORTED_MODULE_0__, _trpc_client__WEBPACK_IMPORTED_MODULE_1__]);\n([_trpc_next__WEBPACK_IMPORTED_MODULE_0__, _trpc_client__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction getBaseUrl() {\n    if (false) {} // browser should use relative url\n    if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`; // SSR should use vercel url\n    return `http://localhost:${process.env.PORT ?? 3000}`; // dev SSR should use localhost\n}\nconst trpc = (0,_trpc_next__WEBPACK_IMPORTED_MODULE_0__.createTRPCNext)({\n    config () {\n        return {\n            links: [\n                (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.splitLink)({\n                    condition (op) {\n                        return op.type === \"subscription\";\n                    },\n                    true: (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.wsLink)({\n                        url: `ws://localhost:3001/trpc`\n                    }),\n                    false: (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.httpBatchLink)({\n                        url: `${getBaseUrl()}/api/trpc`,\n                        headers () {\n                            // Add auth headers here when we implement Auth0\n                            return {};\n                        }\n                    })\n                })\n            ]\n        };\n    },\n    ssr: false\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/trpc.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"../../node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"../../node_modules/@mui/material/node/CssBaseline/index.js\");\n/* harmony import */ var _lib_trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/trpc */ \"./src/lib/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_trpc__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_trpc__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    palette: {\n        mode: \"light\",\n        primary: {\n            main: \"#1976d2\"\n        },\n        secondary: {\n            main: \"#dc004e\"\n        }\n    }\n});\nconst MyApp = ({ Component, pageProps })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        theme: theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_trpc__WEBPACK_IMPORTED_MODULE_1__.trpc.withTRPC(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDa0U7QUFDZDtBQUNsQjtBQUVsQyxNQUFNSSxRQUFRSCxpRUFBV0EsQ0FBQztJQUN4QkksU0FBUztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLFdBQVc7WUFDVEQsTUFBTTtRQUNSO0lBQ0Y7QUFDRjtBQUVBLE1BQU1FLFFBQWlCLENBQUMsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDOUMscUJBQ0UsOERBQUNaLCtEQUFhQTtRQUFDSSxPQUFPQTs7MEJBQ3BCLDhEQUFDRixpRUFBV0E7Ozs7OzBCQUNaLDhEQUFDUztnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7QUFHOUI7QUFFQSxpRUFBZVQsMkNBQUlBLENBQUNVLFFBQVEsQ0FBQ0gsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncDMvd2ViLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBUeXBlIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciwgY3JlYXRlVGhlbWUgfSBmcm9tICdAbXVpL21hdGVyaWFsL3N0eWxlcyc7XG5pbXBvcnQgQ3NzQmFzZWxpbmUgZnJvbSAnQG11aS9tYXRlcmlhbC9Dc3NCYXNlbGluZSc7XG5pbXBvcnQgeyB0cnBjIH0gZnJvbSAnQC9saWIvdHJwYyc7XG5cbmNvbnN0IHRoZW1lID0gY3JlYXRlVGhlbWUoe1xuICBwYWxldHRlOiB7XG4gICAgbW9kZTogJ2xpZ2h0JyxcbiAgICBwcmltYXJ5OiB7XG4gICAgICBtYWluOiAnIzE5NzZkMicsXG4gICAgfSxcbiAgICBzZWNvbmRhcnk6IHtcbiAgICAgIG1haW46ICcjZGMwMDRlJyxcbiAgICB9LFxuICB9LFxufSk7XG5cbmNvbnN0IE15QXBwOiBBcHBUeXBlID0gKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIHRoZW1lPXt0aGVtZX0+XG4gICAgICA8Q3NzQmFzZWxpbmUgLz5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCB0cnBjLndpdGhUUlBDKE15QXBwKTtcbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiY3JlYXRlVGhlbWUiLCJDc3NCYXNlbGluZSIsInRycGMiLCJ0aGVtZSIsInBhbGV0dGUiLCJtb2RlIiwicHJpbWFyeSIsIm1haW4iLCJzZWNvbmRhcnkiLCJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsIndpdGhUUlBDIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "@trpc/client":
/*!*******************************!*\
  !*** external "@trpc/client" ***!
  \*******************************/
/***/ ((module) => {

module.exports = import("@trpc/client");;

/***/ }),

/***/ "@trpc/next":
/*!*****************************!*\
  !*** external "@trpc/next" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("@trpc/next");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@mui","vendor-chunks/@babel"], () => (__webpack_exec__("./src/pages/_app.tsx")));
module.exports = __webpack_exports__;

})();