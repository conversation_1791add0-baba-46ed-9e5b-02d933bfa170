"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/projects/[id]";
exports.ids = ["pages/projects/[id]"];
exports.modules = {

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprojects%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fprojects%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprojects%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fprojects%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"../../node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/projects/[id].tsx */ \"./src/pages/projects/[id].tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/projects/[id]\",\n        pathname: \"/projects/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_projects_id_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprojects%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fprojects%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/lib/trpc.ts":
/*!*************************!*\
  !*** ./src/lib/trpc.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpc: () => (/* binding */ trpc)\n/* harmony export */ });\n/* harmony import */ var _trpc_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/next */ \"@trpc/next\");\n/* harmony import */ var _trpc_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/client */ \"@trpc/client\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc_next__WEBPACK_IMPORTED_MODULE_0__, _trpc_client__WEBPACK_IMPORTED_MODULE_1__]);\n([_trpc_next__WEBPACK_IMPORTED_MODULE_0__, _trpc_client__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction getBaseUrl() {\n    if (false) {} // browser should use relative url\n    if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`; // SSR should use vercel url\n    return `http://localhost:${process.env.PORT ?? 3000}`; // dev SSR should use localhost\n}\nconst trpc = (0,_trpc_next__WEBPACK_IMPORTED_MODULE_0__.createTRPCNext)({\n    config () {\n        return {\n            links: [\n                (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.splitLink)({\n                    condition (op) {\n                        return op.type === \"subscription\";\n                    },\n                    true: (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.wsLink)({\n                        url: `ws://localhost:3001/trpc`\n                    }),\n                    false: (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.httpBatchLink)({\n                        url: `${getBaseUrl()}/api/trpc`,\n                        headers () {\n                            // Add auth headers here when we implement Auth0\n                            return {};\n                        }\n                    })\n                })\n            ]\n        };\n    },\n    ssr: false\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/trpc.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"../../node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"../../node_modules/@mui/material/node/CssBaseline/index.js\");\n/* harmony import */ var _lib_trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/trpc */ \"./src/lib/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_trpc__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_trpc__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    palette: {\n        mode: \"light\",\n        primary: {\n            main: \"#1976d2\"\n        },\n        secondary: {\n            main: \"#dc004e\"\n        }\n    }\n});\nconst MyApp = ({ Component, pageProps })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        theme: theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_trpc__WEBPACK_IMPORTED_MODULE_1__.trpc.withTRPC(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDa0U7QUFDZDtBQUNsQjtBQUVsQyxNQUFNSSxRQUFRSCxpRUFBV0EsQ0FBQztJQUN4QkksU0FBUztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLFdBQVc7WUFDVEQsTUFBTTtRQUNSO0lBQ0Y7QUFDRjtBQUVBLE1BQU1FLFFBQWlCLENBQUMsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDOUMscUJBQ0UsOERBQUNaLCtEQUFhQTtRQUFDSSxPQUFPQTs7MEJBQ3BCLDhEQUFDRixpRUFBV0E7Ozs7OzBCQUNaLDhEQUFDUztnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7QUFHOUI7QUFFQSxpRUFBZVQsMkNBQUlBLENBQUNVLFFBQVEsQ0FBQ0gsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncDMvd2ViLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBUeXBlIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciwgY3JlYXRlVGhlbWUgfSBmcm9tICdAbXVpL21hdGVyaWFsL3N0eWxlcyc7XG5pbXBvcnQgQ3NzQmFzZWxpbmUgZnJvbSAnQG11aS9tYXRlcmlhbC9Dc3NCYXNlbGluZSc7XG5pbXBvcnQgeyB0cnBjIH0gZnJvbSAnQC9saWIvdHJwYyc7XG5cbmNvbnN0IHRoZW1lID0gY3JlYXRlVGhlbWUoe1xuICBwYWxldHRlOiB7XG4gICAgbW9kZTogJ2xpZ2h0JyxcbiAgICBwcmltYXJ5OiB7XG4gICAgICBtYWluOiAnIzE5NzZkMicsXG4gICAgfSxcbiAgICBzZWNvbmRhcnk6IHtcbiAgICAgIG1haW46ICcjZGMwMDRlJyxcbiAgICB9LFxuICB9LFxufSk7XG5cbmNvbnN0IE15QXBwOiBBcHBUeXBlID0gKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIHRoZW1lPXt0aGVtZX0+XG4gICAgICA8Q3NzQmFzZWxpbmUgLz5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCB0cnBjLndpdGhUUlBDKE15QXBwKTtcbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiY3JlYXRlVGhlbWUiLCJDc3NCYXNlbGluZSIsInRycGMiLCJ0aGVtZSIsInBhbGV0dGUiLCJtb2RlIiwicHJpbWFyeSIsIm1haW4iLCJzZWNvbmRhcnkiLCJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsIndpdGhUUlBDIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/projects/[id].tsx":
/*!*************************************!*\
  !*** ./src/pages/projects/[id].tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Breadcrumbs,Button,Card,CardContent,Chip,Container,Dialog,DialogActions,DialogContent,DialogTitle,Fab,Grid,IconButton,Link,TextField,Typography!=!@mui/material */ \"__barrel_optimize__?names=Alert,Box,Breadcrumbs,Button,Card,CardContent,Chip,Container,Dialog,DialogActions,DialogContent,DialogTitle,Fab,Grid,IconButton,Link,TextField,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _barrel_optimize_names_Add_ArrowBack_Chat_Delete_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,ArrowBack,Chat,Delete!=!@mui/icons-material */ \"__barrel_optimize__?names=Add,ArrowBack,Chat,Delete!=!../../node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _lib_trpc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/trpc */ \"./src/lib/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_trpc__WEBPACK_IMPORTED_MODULE_3__]);\n_lib_trpc__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction ProjectPage() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const projectId = router.query.id;\n    const [createDialogOpen, setCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [chatTitle, setChatTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"MEDIUM\");\n    // tRPC queries and mutations\n    const { data: project } = _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.projects.get.useQuery({\n        id: projectId\n    }, {\n        enabled: !!projectId\n    });\n    const { data: chatSessions, isLoading, refetch } = _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.chats.list.useQuery({\n        projectId\n    }, {\n        enabled: !!projectId\n    });\n    const createChat = _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.chats.create.useMutation({\n        onSuccess: ()=>{\n            refetch();\n            setCreateDialogOpen(false);\n            setChatTitle(\"\");\n        }\n    });\n    const deleteChat = _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.chats.delete.useMutation({\n        onSuccess: ()=>{\n            refetch();\n        }\n    });\n    const handleCreateChat = ()=>{\n        if (chatTitle.trim() && projectId) {\n            createChat.mutate({\n                projectId,\n                title: chatTitle.trim(),\n                model: selectedModel\n            });\n        }\n    };\n    const handleDeleteChat = (id)=>{\n        if (confirm(\"Are you sure you want to delete this chat session?\")) {\n            deleteChat.mutate({\n                id\n            });\n        }\n    };\n    const getModelLabel = (model)=>{\n        switch(model){\n            case \"SMALL\":\n                return \"Small (GPT-4.1-mini)\";\n            case \"MEDIUM\":\n                return \"Medium (GPT-4o)\";\n            case \"LARGE\":\n                return \"Large (GPT-o3)\";\n            default:\n                return model;\n        }\n    };\n    const getModelColor = (model)=>{\n        switch(model){\n            case \"SMALL\":\n                return \"success\";\n            case \"MEDIUM\":\n                return \"primary\";\n            case \"LARGE\":\n                return \"warning\";\n            default:\n                return \"default\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n            maxWidth: \"lg\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n        maxWidth: \"lg\",\n        sx: {\n            py: 4\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Breadcrumbs, {\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Link, {\n                                href: \"/\",\n                                underline: \"hover\",\n                                color: \"inherit\",\n                                children: \"Projects\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                color: \"text.primary\",\n                                children: project?.name\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                        sx: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 2,\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                onClick: ()=>router.push(\"/\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_ArrowBack_Chat_Delete_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.ArrowBack, {}, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                children: project?.name\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    project?.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"body1\",\n                        color: \"text.secondary\",\n                        children: project.description\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            chatSessions?.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                severity: \"info\",\n                sx: {\n                    mb: 4\n                },\n                children: \"No chat sessions yet. Create your first chat to get started!\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Grid, {\n                container: true,\n                spacing: 3,\n                children: chatSessions?.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Grid, {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"flex-start\",\n                                            mb: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                variant: \"h6\",\n                                                component: \"h2\",\n                                                children: chat.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                                size: \"small\",\n                                                color: \"error\",\n                                                onClick: ()=>handleDeleteChat(chat.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_ArrowBack_Chat_Delete_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.Delete, {}, void 0, false, {\n                                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                        sx: {\n                                            mb: 2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Chip, {\n                                            label: getModelLabel(chat.model),\n                                            color: getModelColor(chat.model),\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        sx: {\n                                            mb: 2,\n                                            display: \"block\"\n                                        },\n                                        children: [\n                                            chat._count?.messages || 0,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"contained\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_ArrowBack_Chat_Delete_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.Chat, {}, void 0, false, {\n                                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        fullWidth: true,\n                                        href: `/chat/${chat.id}`,\n                                        children: \"Open Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this)\n                    }, chat.id, false, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Fab, {\n                color: \"primary\",\n                \"aria-label\": \"add chat\",\n                sx: {\n                    position: \"fixed\",\n                    bottom: 16,\n                    right: 16\n                },\n                onClick: ()=>setCreateDialogOpen(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_ArrowBack_Chat_Delete_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.Add, {}, void 0, false, {\n                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: createDialogOpen,\n                onClose: ()=>setCreateDialogOpen(false),\n                maxWidth: \"sm\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                        children: \"Create New Chat Session\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.TextField, {\n                                autoFocus: true,\n                                margin: \"dense\",\n                                label: \"Chat Title\",\n                                fullWidth: true,\n                                variant: \"outlined\",\n                                value: chatTitle,\n                                onChange: (e)=>setChatTitle(e.target.value),\n                                sx: {\n                                    mb: 2\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.TextField, {\n                                select: true,\n                                margin: \"dense\",\n                                label: \"AI Model\",\n                                fullWidth: true,\n                                variant: \"outlined\",\n                                value: selectedModel,\n                                onChange: (e)=>setSelectedModel(e.target.value),\n                                SelectProps: {\n                                    native: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SMALL\",\n                                        children: \"Small (GPT-4.1-mini) - Fast & Economical\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MEDIUM\",\n                                        children: \"Medium (GPT-4o) - Balanced\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"LARGE\",\n                                        children: \"Large (GPT-o3) - Most Capable\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.DialogActions, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setCreateDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Breadcrumbs_Button_Card_CardContent_Chip_Container_Dialog_DialogActions_DialogContent_DialogTitle_Fab_Grid_IconButton_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleCreateChat,\n                                variant: \"contained\",\n                                disabled: !chatTitle.trim() || createChat.isLoading,\n                                children: createChat.isLoading ? \"Creating...\" : \"Create\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/projects/[id].tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/projects/[id].tsx\n");

/***/ }),

/***/ "__barrel_optimize__?names=Add,ArrowBack,Chat,Delete!=!../../node_modules/@mui/icons-material/esm/index.js":
/*!*****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Add,ArrowBack,Chat,Delete!=!../../node_modules/@mui/icons-material/esm/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Add: () => (/* reexport safe */ _Add__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ArrowBack: () => (/* reexport safe */ _ArrowBack__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Chat: () => (/* reexport safe */ _Chat__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Delete: () => (/* reexport safe */ _Delete__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Add__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Add */ \"../../node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _ArrowBack__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ArrowBack */ \"../../node_modules/@mui/icons-material/esm/ArrowBack.js\");\n/* harmony import */ var _Chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Chat */ \"../../node_modules/@mui/icons-material/esm/Chat.js\");\n/* harmony import */ var _Delete__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Delete */ \"../../node_modules/@mui/icons-material/esm/Delete.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BZGQsQXJyb3dCYWNrLENoYXQsRGVsZXRlIT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvaWNvbnMtbWF0ZXJpYWwvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ3NDO0FBQ1k7QUFDVjtBQUNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdwMy93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0BtdWkvaWNvbnMtbWF0ZXJpYWwvZXNtL2luZGV4LmpzPzE4ZDQiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFkZCB9IGZyb20gXCIuL0FkZFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93QmFjayB9IGZyb20gXCIuL0Fycm93QmFja1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXQgfSBmcm9tIFwiLi9DaGF0XCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRGVsZXRlIH0gZnJvbSBcIi4vRGVsZXRlXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkFkZCIsIkFycm93QmFjayIsIkNoYXQiLCJEZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Add,ArrowBack,Chat,Delete!=!../../node_modules/@mui/icons-material/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Alert,Box,Breadcrumbs,Button,Card,CardContent,Chip,Container,Dialog,DialogActions,DialogContent,DialogTitle,Fab,Grid,IconButton,Link,TextField,Typography!=!../../node_modules/@mui/material/index.js":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,Box,Breadcrumbs,Button,Card,CardContent,Chip,Container,Dialog,DialogActions,DialogContent,DialogTitle,Fab,Grid,IconButton,Link,TextField,Typography!=!../../node_modules/@mui/material/index.js ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport default from dynamic */ _Alert__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Breadcrumbs: () => (/* reexport default from dynamic */ _Breadcrumbs__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Button: () => (/* reexport default from dynamic */ _Button__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Card: () => (/* reexport default from dynamic */ _Card__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   CardContent: () => (/* reexport default from dynamic */ _CardContent__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_6___default.a),\n/* harmony export */   Container: () => (/* reexport default from dynamic */ _Container__WEBPACK_IMPORTED_MODULE_7___default.a),\n/* harmony export */   Dialog: () => (/* reexport default from dynamic */ _Dialog__WEBPACK_IMPORTED_MODULE_8___default.a),\n/* harmony export */   DialogActions: () => (/* reexport default from dynamic */ _DialogActions__WEBPACK_IMPORTED_MODULE_9___default.a),\n/* harmony export */   DialogContent: () => (/* reexport default from dynamic */ _DialogContent__WEBPACK_IMPORTED_MODULE_10___default.a),\n/* harmony export */   DialogTitle: () => (/* reexport default from dynamic */ _DialogTitle__WEBPACK_IMPORTED_MODULE_11___default.a),\n/* harmony export */   Fab: () => (/* reexport default from dynamic */ _Fab__WEBPACK_IMPORTED_MODULE_12___default.a),\n/* harmony export */   Grid: () => (/* reexport default from dynamic */ _Grid__WEBPACK_IMPORTED_MODULE_13___default.a),\n/* harmony export */   IconButton: () => (/* reexport default from dynamic */ _IconButton__WEBPACK_IMPORTED_MODULE_14___default.a),\n/* harmony export */   Link: () => (/* reexport default from dynamic */ _Link__WEBPACK_IMPORTED_MODULE_15___default.a),\n/* harmony export */   TextField: () => (/* reexport default from dynamic */ _TextField__WEBPACK_IMPORTED_MODULE_16___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_17___default.a)\n/* harmony export */ });\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Alert */ \"../../node_modules/@mui/material/node/Alert/index.js\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Alert__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Breadcrumbs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Breadcrumbs */ \"../../node_modules/@mui/material/node/Breadcrumbs/index.js\");\n/* harmony import */ var _Breadcrumbs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Breadcrumbs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"../../node_modules/@mui/material/node/Button/index.js\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Button__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Card */ \"../../node_modules/@mui/material/node/Card/index.js\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Card__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CardContent */ \"../../node_modules/@mui/material/node/CardContent/index.js\");\n/* harmony import */ var _CardContent__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_CardContent__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Container */ \"../../node_modules/@mui/material/node/Container/index.js\");\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_Container__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Dialog */ \"../../node_modules/@mui/material/node/Dialog/index.js\");\n/* harmony import */ var _Dialog__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_Dialog__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _DialogActions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DialogActions */ \"../../node_modules/@mui/material/node/DialogActions/index.js\");\n/* harmony import */ var _DialogActions__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_DialogActions__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _DialogContent__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./DialogContent */ \"../../node_modules/@mui/material/node/DialogContent/index.js\");\n/* harmony import */ var _DialogContent__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_DialogContent__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _DialogTitle__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./DialogTitle */ \"../../node_modules/@mui/material/node/DialogTitle/index.js\");\n/* harmony import */ var _DialogTitle__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_DialogTitle__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _Fab__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Fab */ \"../../node_modules/@mui/material/node/Fab/index.js\");\n/* harmony import */ var _Fab__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_Fab__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Grid */ \"../../node_modules/@mui/material/node/Grid/index.js\");\n/* harmony import */ var _Grid__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_Grid__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./IconButton */ \"../../node_modules/@mui/material/node/IconButton/index.js\");\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_IconButton__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _Link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./Link */ \"../../node_modules/@mui/material/node/Link/index.js\");\n/* harmony import */ var _Link__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_Link__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _TextField__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./TextField */ \"../../node_modules/@mui/material/node/TextField/index.js\");\n/* harmony import */ var _TextField__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_TextField__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_17__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Alert,Box,Breadcrumbs,Button,Card,CardContent,Chip,Container,Dialog,DialogActions,DialogContent,DialogTitle,Fab,Grid,IconButton,Link,TextField,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/RtlProvider":
/*!******************************************!*\
  !*** external "@mui/system/RtlProvider" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/RtlProvider");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/utils":
/*!*****************************!*\
  !*** external "@mui/utils" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("@mui/utils");

/***/ }),

/***/ "@mui/utils/HTMLElementType":
/*!*********************************************!*\
  !*** external "@mui/utils/HTMLElementType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/HTMLElementType");

/***/ }),

/***/ "@mui/utils/appendOwnerState":
/*!**********************************************!*\
  !*** external "@mui/utils/appendOwnerState" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/appendOwnerState");

/***/ }),

/***/ "@mui/utils/capitalize":
/*!****************************************!*\
  !*** external "@mui/utils/capitalize" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/capitalize");

/***/ }),

/***/ "@mui/utils/chainPropTypes":
/*!********************************************!*\
  !*** external "@mui/utils/chainPropTypes" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/chainPropTypes");

/***/ }),

/***/ "@mui/utils/composeClasses":
/*!********************************************!*\
  !*** external "@mui/utils/composeClasses" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/composeClasses");

/***/ }),

/***/ "@mui/utils/createChainedFunction":
/*!***************************************************!*\
  !*** external "@mui/utils/createChainedFunction" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/createChainedFunction");

/***/ }),

/***/ "@mui/utils/debounce":
/*!**************************************!*\
  !*** external "@mui/utils/debounce" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/debounce");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/deprecatedPropType":
/*!************************************************!*\
  !*** external "@mui/utils/deprecatedPropType" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deprecatedPropType");

/***/ }),

/***/ "@mui/utils/elementAcceptingRef":
/*!*************************************************!*\
  !*** external "@mui/utils/elementAcceptingRef" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementAcceptingRef");

/***/ }),

/***/ "@mui/utils/elementTypeAcceptingRef":
/*!*****************************************************!*\
  !*** external "@mui/utils/elementTypeAcceptingRef" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementTypeAcceptingRef");

/***/ }),

/***/ "@mui/utils/extractEventHandlers":
/*!**************************************************!*\
  !*** external "@mui/utils/extractEventHandlers" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/extractEventHandlers");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "@mui/utils/generateUtilityClasses":
/*!****************************************************!*\
  !*** external "@mui/utils/generateUtilityClasses" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClasses");

/***/ }),

/***/ "@mui/utils/getReactElementRef":
/*!************************************************!*\
  !*** external "@mui/utils/getReactElementRef" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getReactElementRef");

/***/ }),

/***/ "@mui/utils/getScrollbarSize":
/*!**********************************************!*\
  !*** external "@mui/utils/getScrollbarSize" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getScrollbarSize");

/***/ }),

/***/ "@mui/utils/integerPropType":
/*!*********************************************!*\
  !*** external "@mui/utils/integerPropType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/integerPropType");

/***/ }),

/***/ "@mui/utils/isHostComponent":
/*!*********************************************!*\
  !*** external "@mui/utils/isHostComponent" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/isHostComponent");

/***/ }),

/***/ "@mui/utils/isMuiElement":
/*!******************************************!*\
  !*** external "@mui/utils/isMuiElement" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/isMuiElement");

/***/ }),

/***/ "@mui/utils/mergeSlotProps":
/*!********************************************!*\
  !*** external "@mui/utils/mergeSlotProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/mergeSlotProps");

/***/ }),

/***/ "@mui/utils/ownerDocument":
/*!*******************************************!*\
  !*** external "@mui/utils/ownerDocument" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/ownerDocument");

/***/ }),

/***/ "@mui/utils/ownerWindow":
/*!*****************************************!*\
  !*** external "@mui/utils/ownerWindow" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/ownerWindow");

/***/ }),

/***/ "@mui/utils/refType":
/*!*************************************!*\
  !*** external "@mui/utils/refType" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/refType");

/***/ }),

/***/ "@mui/utils/requirePropFactory":
/*!************************************************!*\
  !*** external "@mui/utils/requirePropFactory" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/requirePropFactory");

/***/ }),

/***/ "@mui/utils/resolveComponentProps":
/*!***************************************************!*\
  !*** external "@mui/utils/resolveComponentProps" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveComponentProps");

/***/ }),

/***/ "@mui/utils/resolveProps":
/*!******************************************!*\
  !*** external "@mui/utils/resolveProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveProps");

/***/ }),

/***/ "@mui/utils/setRef":
/*!************************************!*\
  !*** external "@mui/utils/setRef" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/setRef");

/***/ }),

/***/ "@mui/utils/unsupportedProp":
/*!*********************************************!*\
  !*** external "@mui/utils/unsupportedProp" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/unsupportedProp");

/***/ }),

/***/ "@mui/utils/useControlled":
/*!*******************************************!*\
  !*** external "@mui/utils/useControlled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useControlled");

/***/ }),

/***/ "@mui/utils/useEnhancedEffect":
/*!***********************************************!*\
  !*** external "@mui/utils/useEnhancedEffect" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEnhancedEffect");

/***/ }),

/***/ "@mui/utils/useEventCallback":
/*!**********************************************!*\
  !*** external "@mui/utils/useEventCallback" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEventCallback");

/***/ }),

/***/ "@mui/utils/useForkRef":
/*!****************************************!*\
  !*** external "@mui/utils/useForkRef" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useForkRef");

/***/ }),

/***/ "@mui/utils/useId":
/*!***********************************!*\
  !*** external "@mui/utils/useId" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useId");

/***/ }),

/***/ "@mui/utils/useIsFocusVisible":
/*!***********************************************!*\
  !*** external "@mui/utils/useIsFocusVisible" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useIsFocusVisible");

/***/ }),

/***/ "@mui/utils/useSlotProps":
/*!******************************************!*\
  !*** external "@mui/utils/useSlotProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useSlotProps");

/***/ }),

/***/ "@mui/utils/useTimeout":
/*!****************************************!*\
  !*** external "@mui/utils/useTimeout" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useTimeout");

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("clsx");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react-is":
/*!***************************!*\
  !*** external "react-is" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("react-is");

/***/ }),

/***/ "react-transition-group":
/*!*****************************************!*\
  !*** external "react-transition-group" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("react-transition-group");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "@trpc/client":
/*!*******************************!*\
  !*** external "@trpc/client" ***!
  \*******************************/
/***/ ((module) => {

module.exports = import("@trpc/client");;

/***/ }),

/***/ "@trpc/next":
/*!*****************************!*\
  !*** external "@trpc/next" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("@trpc/next");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@swc","vendor-chunks/@babel"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprojects%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fprojects%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();