"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/chat/[id]";
exports.ids = ["pages/chat/[id]"];
exports.modules = {

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fchat%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fchat%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"../../node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/chat/[id].tsx */ \"./src/pages/chat/[id].tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/chat/[id]\",\n        pathname: \"/chat/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_chat_id_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fchat%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/lib/trpc.ts":
/*!*************************!*\
  !*** ./src/lib/trpc.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpc: () => (/* binding */ trpc)\n/* harmony export */ });\n/* harmony import */ var _trpc_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/next */ \"@trpc/next\");\n/* harmony import */ var _trpc_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/client */ \"@trpc/client\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_trpc_next__WEBPACK_IMPORTED_MODULE_0__, _trpc_client__WEBPACK_IMPORTED_MODULE_1__]);\n([_trpc_next__WEBPACK_IMPORTED_MODULE_0__, _trpc_client__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction getBaseUrl() {\n    if (false) {} // browser should use relative url\n    if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`; // SSR should use vercel url\n    return `http://localhost:${process.env.PORT ?? 3000}`; // dev SSR should use localhost\n}\nconst trpc = (0,_trpc_next__WEBPACK_IMPORTED_MODULE_0__.createTRPCNext)({\n    config () {\n        return {\n            links: [\n                (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.splitLink)({\n                    condition (op) {\n                        return op.type === \"subscription\";\n                    },\n                    true: (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.wsLink)({\n                        url: `ws://localhost:3001/trpc`\n                    }),\n                    false: (0,_trpc_client__WEBPACK_IMPORTED_MODULE_1__.httpBatchLink)({\n                        url: `${getBaseUrl()}/api/trpc`,\n                        headers () {\n                            // Add auth headers here when we implement Auth0\n                            return {};\n                        }\n                    })\n                })\n            ]\n        };\n    },\n    ssr: false\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/trpc.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"../../node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"../../node_modules/@mui/material/node/CssBaseline/index.js\");\n/* harmony import */ var _lib_trpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/trpc */ \"./src/lib/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_trpc__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_trpc__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    palette: {\n        mode: \"light\",\n        primary: {\n            main: \"#1976d2\"\n        },\n        secondary: {\n            main: \"#dc004e\"\n        }\n    }\n});\nconst MyApp = ({ Component, pageProps })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        theme: theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_lib_trpc__WEBPACK_IMPORTED_MODULE_1__.trpc.withTRPC(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDa0U7QUFDZDtBQUNsQjtBQUVsQyxNQUFNSSxRQUFRSCxpRUFBV0EsQ0FBQztJQUN4QkksU0FBUztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLFdBQVc7WUFDVEQsTUFBTTtRQUNSO0lBQ0Y7QUFDRjtBQUVBLE1BQU1FLFFBQWlCLENBQUMsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDOUMscUJBQ0UsOERBQUNaLCtEQUFhQTtRQUFDSSxPQUFPQTs7MEJBQ3BCLDhEQUFDRixpRUFBV0E7Ozs7OzBCQUNaLDhEQUFDUztnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7QUFHOUI7QUFFQSxpRUFBZVQsMkNBQUlBLENBQUNVLFFBQVEsQ0FBQ0gsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncDMvd2ViLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBUeXBlIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciwgY3JlYXRlVGhlbWUgfSBmcm9tICdAbXVpL21hdGVyaWFsL3N0eWxlcyc7XG5pbXBvcnQgQ3NzQmFzZWxpbmUgZnJvbSAnQG11aS9tYXRlcmlhbC9Dc3NCYXNlbGluZSc7XG5pbXBvcnQgeyB0cnBjIH0gZnJvbSAnQC9saWIvdHJwYyc7XG5cbmNvbnN0IHRoZW1lID0gY3JlYXRlVGhlbWUoe1xuICBwYWxldHRlOiB7XG4gICAgbW9kZTogJ2xpZ2h0JyxcbiAgICBwcmltYXJ5OiB7XG4gICAgICBtYWluOiAnIzE5NzZkMicsXG4gICAgfSxcbiAgICBzZWNvbmRhcnk6IHtcbiAgICAgIG1haW46ICcjZGMwMDRlJyxcbiAgICB9LFxuICB9LFxufSk7XG5cbmNvbnN0IE15QXBwOiBBcHBUeXBlID0gKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIHRoZW1lPXt0aGVtZX0+XG4gICAgICA8Q3NzQmFzZWxpbmUgLz5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCB0cnBjLndpdGhUUlBDKE15QXBwKTtcbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiY3JlYXRlVGhlbWUiLCJDc3NCYXNlbGluZSIsInRycGMiLCJ0aGVtZSIsInBhbGV0dGUiLCJtb2RlIiwicHJpbWFyeSIsIm1haW4iLCJzZWNvbmRhcnkiLCJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsIndpdGhUUlBDIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/chat/[id].tsx":
/*!*********************************!*\
  !*** ./src/pages/chat/[id].tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Breadcrumbs,Chip,Container,IconButton,Link,Paper,TextField,Typography!=!@mui/material */ \"__barrel_optimize__?names=Alert,Avatar,Box,Breadcrumbs,Chip,Container,IconButton,Link,Paper,TextField,Typography!=!../../node_modules/@mui/material/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowBack_Person_Send_SmartToy_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowBack,Person,Send,SmartToy!=!@mui/icons-material */ \"__barrel_optimize__?names=ArrowBack,Person,Send,SmartToy!=!../../node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _lib_trpc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/trpc */ \"./src/lib/trpc.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_trpc__WEBPACK_IMPORTED_MODULE_3__]);\n_lib_trpc__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction ChatPage() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const chatId = router.query.id;\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // tRPC queries and mutations\n    const { data: chatSession, isLoading } = _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.chats.get.useQuery({\n        id: chatId\n    }, {\n        enabled: !!chatId\n    });\n    const sendMessage = _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.chats.sendMessage.useMutation({\n        onSuccess: ()=>{\n            // Refetch chat data to get new messages\n            _lib_trpc__WEBPACK_IMPORTED_MODULE_3__.trpc.chats.get.invalidate({\n                id: chatId\n            });\n            setMessage(\"\");\n        }\n    });\n    const handleSendMessage = ()=>{\n        if (message.trim() && chatId) {\n            sendMessage.mutate({\n                chatSessionId: chatId,\n                content: message.trim()\n            });\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const getModelLabel = (model)=>{\n        switch(model){\n            case \"SMALL\":\n                return \"Small (GPT-4.1-mini)\";\n            case \"MEDIUM\":\n                return \"Medium (GPT-4o)\";\n            case \"LARGE\":\n                return \"Large (GPT-o3)\";\n            default:\n                return model;\n        }\n    };\n    const getModelColor = (model)=>{\n        switch(model){\n            case \"SMALL\":\n                return \"success\";\n            case \"MEDIUM\":\n                return \"primary\";\n            case \"LARGE\":\n                return \"warning\";\n            default:\n                return \"default\";\n        }\n    };\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatSession?.messages\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n            maxWidth: \"lg\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chatSession) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n            maxWidth: \"lg\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                severity: \"error\",\n                children: \"Chat session not found\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        sx: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Paper, {\n                elevation: 1,\n                sx: {\n                    p: 2,\n                    borderRadius: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxWidth: \"lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Breadcrumbs, {\n                            sx: {\n                                mb: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Link, {\n                                    href: \"/\",\n                                    underline: \"hover\",\n                                    color: \"inherit\",\n                                    children: \"Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Link, {\n                                    href: `/projects/${chatSession.project.id}`,\n                                    underline: \"hover\",\n                                    color: \"inherit\",\n                                    children: chatSession.project.name\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    color: \"text.primary\",\n                                    children: chatSession.title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                    onClick: ()=>router.push(`/projects/${chatSession.project.id}`),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowBack_Person_Send_SmartToy_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.ArrowBack, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"h6\",\n                                    component: \"h1\",\n                                    children: chatSession.title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Chip, {\n                                    label: getModelLabel(chatSession.model),\n                                    color: getModelColor(chatSession.model),\n                                    size: \"small\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                sx: {\n                    flex: 1,\n                    overflow: \"auto\",\n                    p: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxWidth: \"lg\",\n                    children: chatSession.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                        severity: \"info\",\n                        sx: {\n                            mt: 4\n                        },\n                        children: \"No messages yet. Start the conversation!\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: 2\n                        },\n                        children: [\n                            chatSession.messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"flex-start\",\n                                        gap: 2,\n                                        flexDirection: msg.role === \"USER\" ? \"row-reverse\" : \"row\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                            sx: {\n                                                bgcolor: msg.role === \"USER\" ? \"primary.main\" : \"secondary.main\"\n                                            },\n                                            children: msg.role === \"USER\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowBack_Person_Send_SmartToy_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.Person, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 44\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowBack_Person_Send_SmartToy_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.SmartToy, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 61\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Paper, {\n                                            elevation: 1,\n                                            sx: {\n                                                p: 2,\n                                                maxWidth: \"70%\",\n                                                bgcolor: msg.role === \"USER\" ? \"primary.light\" : \"grey.100\",\n                                                color: msg.role === \"USER\" ? \"primary.contrastText\" : \"text.primary\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    variant: \"body1\",\n                                                    sx: {\n                                                        whiteSpace: \"pre-wrap\"\n                                                    },\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        opacity: 0.7,\n                                                        mt: 1,\n                                                        display: \"block\"\n                                                    },\n                                                    children: new Date(msg.createdAt).toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, msg.id, true, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                        lineNumber: 138,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Paper, {\n                elevation: 3,\n                sx: {\n                    p: 2,\n                    borderRadius: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Container, {\n                    maxWidth: \"lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                        sx: {\n                            display: \"flex\",\n                            gap: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.TextField, {\n                                fullWidth: true,\n                                multiline: true,\n                                maxRows: 4,\n                                placeholder: \"Type your message...\",\n                                value: message,\n                                onChange: (e)=>setMessage(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                disabled: sendMessage.isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Breadcrumbs_Chip_Container_IconButton_Link_Paper_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                color: \"primary\",\n                                onClick: handleSendMessage,\n                                disabled: !message.trim() || sendMessage.isLoading,\n                                sx: {\n                                    alignSelf: \"flex-end\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowBack_Person_Send_SmartToy_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__.Send, {}, void 0, false, {\n                                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/coding/gp3/v1/apps/web/src/pages/chat/[id].tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat/[id].tsx\n");

/***/ }),

/***/ "__barrel_optimize__?names=Alert,Avatar,Box,Breadcrumbs,Chip,Container,IconButton,Link,Paper,TextField,Typography!=!../../node_modules/@mui/material/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,Avatar,Box,Breadcrumbs,Chip,Container,IconButton,Link,Paper,TextField,Typography!=!../../node_modules/@mui/material/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport default from dynamic */ _Alert__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Avatar: () => (/* reexport default from dynamic */ _Avatar__WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   Box: () => (/* reexport default from dynamic */ _Box__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   Breadcrumbs: () => (/* reexport default from dynamic */ _Breadcrumbs__WEBPACK_IMPORTED_MODULE_3___default.a),\n/* harmony export */   Chip: () => (/* reexport default from dynamic */ _Chip__WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   Container: () => (/* reexport default from dynamic */ _Container__WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   IconButton: () => (/* reexport default from dynamic */ _IconButton__WEBPACK_IMPORTED_MODULE_6___default.a),\n/* harmony export */   Link: () => (/* reexport default from dynamic */ _Link__WEBPACK_IMPORTED_MODULE_7___default.a),\n/* harmony export */   Paper: () => (/* reexport default from dynamic */ _Paper__WEBPACK_IMPORTED_MODULE_8___default.a),\n/* harmony export */   TextField: () => (/* reexport default from dynamic */ _TextField__WEBPACK_IMPORTED_MODULE_9___default.a),\n/* harmony export */   Typography: () => (/* reexport default from dynamic */ _Typography__WEBPACK_IMPORTED_MODULE_10___default.a)\n/* harmony export */ });\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Alert */ \"../../node_modules/@mui/material/node/Alert/index.js\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Alert__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Avatar */ \"../../node_modules/@mui/material/node/Avatar/index.js\");\n/* harmony import */ var _Avatar__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Avatar__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Box */ \"../../node_modules/@mui/material/node/Box/index.js\");\n/* harmony import */ var _Box__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Box__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Breadcrumbs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Breadcrumbs */ \"../../node_modules/@mui/material/node/Breadcrumbs/index.js\");\n/* harmony import */ var _Breadcrumbs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Breadcrumbs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Chip */ \"../../node_modules/@mui/material/node/Chip/index.js\");\n/* harmony import */ var _Chip__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Chip__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Container */ \"../../node_modules/@mui/material/node/Container/index.js\");\n/* harmony import */ var _Container__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_Container__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./IconButton */ \"../../node_modules/@mui/material/node/IconButton/index.js\");\n/* harmony import */ var _IconButton__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_IconButton__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Link */ \"../../node_modules/@mui/material/node/Link/index.js\");\n/* harmony import */ var _Link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_Link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _Paper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Paper */ \"../../node_modules/@mui/material/node/Paper/index.js\");\n/* harmony import */ var _Paper__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_Paper__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _TextField__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./TextField */ \"../../node_modules/@mui/material/node/TextField/index.js\");\n/* harmony import */ var _TextField__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_TextField__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Typography */ \"../../node_modules/@mui/material/node/Typography/index.js\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_Typography__WEBPACK_IMPORTED_MODULE_10__);\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydCxBdmF0YXIsQm94LEJyZWFkY3J1bWJzLENoaXAsQ29udGFpbmVyLEljb25CdXR0b24sTGluayxQYXBlcixUZXh0RmllbGQsVHlwb2dyYXBoeSE9IS4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUMwQztBQUNFO0FBQ047QUFDZ0I7QUFDZDtBQUNVO0FBQ0U7QUFDWjtBQUNFO0FBQ1E7QUFDRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BncDMvd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2luZGV4LmpzPzc1NmMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFsZXJ0IH0gZnJvbSBcIi4vQWxlcnRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBdmF0YXIgfSBmcm9tIFwiLi9BdmF0YXJcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3hcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCcmVhZGNydW1icyB9IGZyb20gXCIuL0JyZWFkY3J1bWJzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hpcCB9IGZyb20gXCIuL0NoaXBcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb250YWluZXIgfSBmcm9tIFwiLi9Db250YWluZXJcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBJY29uQnV0dG9uIH0gZnJvbSBcIi4vSWNvbkJ1dHRvblwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExpbmsgfSBmcm9tIFwiLi9MaW5rXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFwZXIgfSBmcm9tIFwiLi9QYXBlclwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRleHRGaWVsZCB9IGZyb20gXCIuL1RleHRGaWVsZFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFR5cG9ncmFwaHkgfSBmcm9tIFwiLi9UeXBvZ3JhcGh5XCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkFsZXJ0IiwiQXZhdGFyIiwiQm94IiwiQnJlYWRjcnVtYnMiLCJDaGlwIiwiQ29udGFpbmVyIiwiSWNvbkJ1dHRvbiIsIkxpbmsiLCJQYXBlciIsIlRleHRGaWVsZCIsIlR5cG9ncmFwaHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Alert,Avatar,Box,Breadcrumbs,Chip,Container,IconButton,Link,Paper,TextField,Typography!=!../../node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowBack,Person,Send,SmartToy!=!../../node_modules/@mui/icons-material/esm/index.js":
/*!**********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowBack,Person,Send,SmartToy!=!../../node_modules/@mui/icons-material/esm/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowBack: () => (/* reexport safe */ _ArrowBack__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Person: () => (/* reexport safe */ _Person__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _Send__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   SmartToy: () => (/* reexport safe */ _SmartToy__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowBack__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowBack */ \"../../node_modules/@mui/icons-material/esm/ArrowBack.js\");\n/* harmony import */ var _Person__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Person */ \"../../node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _Send__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Send */ \"../../node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _SmartToy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SmartToy */ \"../../node_modules/@mui/icons-material/esm/SmartToy.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0JhY2ssUGVyc29uLFNlbmQsU21hcnRUb3khPSEuLi8uLi9ub2RlX21vZHVsZXMvQG11aS9pY29ucy1tYXRlcmlhbC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDTjtBQUNKO0FBQ1EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3AzL3dlYi8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9pY29ucy1tYXRlcmlhbC9lc20vaW5kZXguanM/MThkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dCYWNrIH0gZnJvbSBcIi4vQXJyb3dCYWNrXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGVyc29uIH0gZnJvbSBcIi4vUGVyc29uXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2VuZCB9IGZyb20gXCIuL1NlbmRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTbWFydFRveSB9IGZyb20gXCIuL1NtYXJ0VG95XCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkFycm93QmFjayIsIlBlcnNvbiIsIlNlbmQiLCJTbWFydFRveSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowBack,Person,Send,SmartToy!=!../../node_modules/@mui/icons-material/esm/index.js\n");

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/RtlProvider":
/*!******************************************!*\
  !*** external "@mui/system/RtlProvider" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/RtlProvider");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/utils":
/*!*****************************!*\
  !*** external "@mui/utils" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("@mui/utils");

/***/ }),

/***/ "@mui/utils/HTMLElementType":
/*!*********************************************!*\
  !*** external "@mui/utils/HTMLElementType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/HTMLElementType");

/***/ }),

/***/ "@mui/utils/appendOwnerState":
/*!**********************************************!*\
  !*** external "@mui/utils/appendOwnerState" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/appendOwnerState");

/***/ }),

/***/ "@mui/utils/capitalize":
/*!****************************************!*\
  !*** external "@mui/utils/capitalize" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/capitalize");

/***/ }),

/***/ "@mui/utils/chainPropTypes":
/*!********************************************!*\
  !*** external "@mui/utils/chainPropTypes" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/chainPropTypes");

/***/ }),

/***/ "@mui/utils/composeClasses":
/*!********************************************!*\
  !*** external "@mui/utils/composeClasses" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/composeClasses");

/***/ }),

/***/ "@mui/utils/createChainedFunction":
/*!***************************************************!*\
  !*** external "@mui/utils/createChainedFunction" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/createChainedFunction");

/***/ }),

/***/ "@mui/utils/debounce":
/*!**************************************!*\
  !*** external "@mui/utils/debounce" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/debounce");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/deprecatedPropType":
/*!************************************************!*\
  !*** external "@mui/utils/deprecatedPropType" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deprecatedPropType");

/***/ }),

/***/ "@mui/utils/elementAcceptingRef":
/*!*************************************************!*\
  !*** external "@mui/utils/elementAcceptingRef" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementAcceptingRef");

/***/ }),

/***/ "@mui/utils/elementTypeAcceptingRef":
/*!*****************************************************!*\
  !*** external "@mui/utils/elementTypeAcceptingRef" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementTypeAcceptingRef");

/***/ }),

/***/ "@mui/utils/extractEventHandlers":
/*!**************************************************!*\
  !*** external "@mui/utils/extractEventHandlers" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/extractEventHandlers");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "@mui/utils/generateUtilityClasses":
/*!****************************************************!*\
  !*** external "@mui/utils/generateUtilityClasses" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClasses");

/***/ }),

/***/ "@mui/utils/getReactElementRef":
/*!************************************************!*\
  !*** external "@mui/utils/getReactElementRef" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getReactElementRef");

/***/ }),

/***/ "@mui/utils/getScrollbarSize":
/*!**********************************************!*\
  !*** external "@mui/utils/getScrollbarSize" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getScrollbarSize");

/***/ }),

/***/ "@mui/utils/integerPropType":
/*!*********************************************!*\
  !*** external "@mui/utils/integerPropType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/integerPropType");

/***/ }),

/***/ "@mui/utils/isHostComponent":
/*!*********************************************!*\
  !*** external "@mui/utils/isHostComponent" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/isHostComponent");

/***/ }),

/***/ "@mui/utils/isMuiElement":
/*!******************************************!*\
  !*** external "@mui/utils/isMuiElement" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/isMuiElement");

/***/ }),

/***/ "@mui/utils/mergeSlotProps":
/*!********************************************!*\
  !*** external "@mui/utils/mergeSlotProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/mergeSlotProps");

/***/ }),

/***/ "@mui/utils/ownerDocument":
/*!*******************************************!*\
  !*** external "@mui/utils/ownerDocument" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/ownerDocument");

/***/ }),

/***/ "@mui/utils/ownerWindow":
/*!*****************************************!*\
  !*** external "@mui/utils/ownerWindow" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/ownerWindow");

/***/ }),

/***/ "@mui/utils/refType":
/*!*************************************!*\
  !*** external "@mui/utils/refType" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/refType");

/***/ }),

/***/ "@mui/utils/requirePropFactory":
/*!************************************************!*\
  !*** external "@mui/utils/requirePropFactory" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/requirePropFactory");

/***/ }),

/***/ "@mui/utils/resolveComponentProps":
/*!***************************************************!*\
  !*** external "@mui/utils/resolveComponentProps" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveComponentProps");

/***/ }),

/***/ "@mui/utils/setRef":
/*!************************************!*\
  !*** external "@mui/utils/setRef" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/setRef");

/***/ }),

/***/ "@mui/utils/unsupportedProp":
/*!*********************************************!*\
  !*** external "@mui/utils/unsupportedProp" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/unsupportedProp");

/***/ }),

/***/ "@mui/utils/useControlled":
/*!*******************************************!*\
  !*** external "@mui/utils/useControlled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useControlled");

/***/ }),

/***/ "@mui/utils/useEnhancedEffect":
/*!***********************************************!*\
  !*** external "@mui/utils/useEnhancedEffect" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEnhancedEffect");

/***/ }),

/***/ "@mui/utils/useEventCallback":
/*!**********************************************!*\
  !*** external "@mui/utils/useEventCallback" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEventCallback");

/***/ }),

/***/ "@mui/utils/useForkRef":
/*!****************************************!*\
  !*** external "@mui/utils/useForkRef" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useForkRef");

/***/ }),

/***/ "@mui/utils/useId":
/*!***********************************!*\
  !*** external "@mui/utils/useId" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useId");

/***/ }),

/***/ "@mui/utils/useIsFocusVisible":
/*!***********************************************!*\
  !*** external "@mui/utils/useIsFocusVisible" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useIsFocusVisible");

/***/ }),

/***/ "@mui/utils/useSlotProps":
/*!******************************************!*\
  !*** external "@mui/utils/useSlotProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useSlotProps");

/***/ }),

/***/ "@mui/utils/useTimeout":
/*!****************************************!*\
  !*** external "@mui/utils/useTimeout" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useTimeout");

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("clsx");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react-is":
/*!***************************!*\
  !*** external "react-is" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("react-is");

/***/ }),

/***/ "react-transition-group":
/*!*****************************************!*\
  !*** external "react-transition-group" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("react-transition-group");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "@trpc/client":
/*!*******************************!*\
  !*** external "@trpc/client" ***!
  \*******************************/
/***/ ((module) => {

module.exports = import("@trpc/client");;

/***/ }),

/***/ "@trpc/next":
/*!*****************************!*\
  !*** external "@trpc/next" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("@trpc/next");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@swc","vendor-chunks/@babel"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat%2F%5Bid%5D&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fchat%2F%5Bid%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();