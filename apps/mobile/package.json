{"name": "@gp3/mobile", "version": "0.1.0", "private": true, "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "type-check": "tsc --noEmit"}, "dependencies": {"@gp3/shared": "*", "@gp3/trpc": "*", "@expo/vector-icons": "^13.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@tanstack/react-query": "^5.8.4", "@trpc/client": "^10.45.0", "@trpc/react-query": "^10.45.0", "expo": "~49.0.15", "expo-router": "^2.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-paper": "^5.11.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}