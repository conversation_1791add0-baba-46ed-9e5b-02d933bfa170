import React, { useState } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { 
  Card, 
  Title, 
  Paragraph, 
  Button, 
  FAB, 
  Portal, 
  Dialog, 
  TextInput,
  Text,
  IconButton
} from 'react-native-paper';
import { router } from 'expo-router';
import { trpc } from '@gp3/trpc';

export default function ProjectsScreen() {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');

  const { data: projects, isLoading, refetch } = trpc.projects.list.useQuery();
  const createProject = trpc.projects.create.useMutation({
    onSuccess: () => {
      refetch();
      setDialogVisible(false);
      setProjectName('');
      setProjectDescription('');
    },
  });
  const deleteProject = trpc.projects.delete.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleCreateProject = () => {
    if (projectName.trim()) {
      createProject.mutate({
        name: projectName.trim(),
        description: projectDescription.trim() || undefined,
      });
    }
  };

  const handleDeleteProject = (id: string) => {
    deleteProject.mutate({ id });
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {projects?.map((project) => (
          <Card key={project.id} style={styles.card}>
            <Card.Content>
              <View style={styles.cardHeader}>
                <Title>{project.name}</Title>
                <IconButton
                  icon="delete"
                  size={20}
                  onPress={() => handleDeleteProject(project.id)}
                />
              </View>
              {project.description && (
                <Paragraph>{project.description}</Paragraph>
              )}
              <Text style={styles.chatCount}>
                {project._count?.chatSessions || 0} chat sessions
              </Text>
            </Card.Content>
            <Card.Actions>
              <Button 
                mode="contained" 
                onPress={() => router.push(`/project/${project.id}`)}
              >
                Open Project
              </Button>
            </Card.Actions>
          </Card>
        ))}
      </ScrollView>

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => setDialogVisible(true)}
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>Create New Project</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Project Name"
              value={projectName}
              onChangeText={setProjectName}
              style={styles.input}
            />
            <TextInput
              label="Description (optional)"
              value={projectDescription}
              onChangeText={setProjectDescription}
              multiline
              numberOfLines={3}
              style={styles.input}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button 
              mode="contained" 
              onPress={handleCreateProject}
              disabled={!projectName.trim() || createProject.isLoading}
            >
              {createProject.isLoading ? 'Creating...' : 'Create'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chatCount: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  input: {
    marginBottom: 16,
  },
});
