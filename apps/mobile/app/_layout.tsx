import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { PaperProvider } from 'react-native-paper';
import { TRPCProvider } from '@gp3/trpc';

export default function RootLayout() {
  return (
    <TRPCProvider apiUrl="http://localhost:3000/api/trpc">
      <PaperProvider>
        <Stack>
          <Stack.Screen name="index" options={{ title: 'Projects' }} />
          <Stack.Screen name="project/[id]" options={{ title: 'Project' }} />
          <Stack.Screen name="chat/[id]" options={{ title: 'Chat' }} />
        </Stack>
        <StatusBar style="auto" />
      </PaperProvider>
    </TRPCProvider>
  );
}
