{"name": "@mui/icons-material", "version": "5.17.1", "private": false, "author": "MUI Team", "description": "Material Design icons distributed as SVG React components.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "material-ui", "material design", "icons"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-icons-material"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://5.mui.com/material-ui/material-icons/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.23.9"}, "peerDependencies": {"@mui/material": "^5.0.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=12.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}