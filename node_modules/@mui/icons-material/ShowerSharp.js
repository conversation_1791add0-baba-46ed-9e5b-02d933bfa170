"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "8",
  cy: "17",
  r: "1"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "12",
  cy: "17",
  r: "1"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "16",
  cy: "17",
  r: "1"
}, "2"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M13 5.08V3h-2v2.08C7.61 5.57 5 8.47 5 12v2h14v-2c0-3.53-2.61-6.43-6-6.92"
}, "3"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "8",
  cy: "20",
  r: "1"
}, "4"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "12",
  cy: "20",
  r: "1"
}, "5"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "16",
  cy: "20",
  r: "1"
}, "6")], 'ShowerSharp');